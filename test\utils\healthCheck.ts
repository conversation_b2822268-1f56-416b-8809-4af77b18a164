import { API_BASE_URL } from '../config/api';

/**
 * Check if the backend server is accessible
 */
export const checkBackendHealth = async (): Promise<{ 
  success: boolean; 
  message: string; 
  url: string;
}> => {
  const healthUrl = `${API_BASE_URL}/`;
  
  try {
    console.log(`[Health Check] Checking backend at: ${healthUrl}`);
    
    const response = await fetch(healthUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add timeout for mobile networks
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('[Health Check] Backend is healthy:', data);
      return {
        success: true,
        message: 'Backend server is accessible',
        url: healthUrl
      };
    } else {
      console.error(`[Health Check] Backend returned ${response.status}`);
      return {
        success: false,
        message: `Backend returned status ${response.status}`,
        url: healthUrl
      };
    }
  } catch (error) {
    console.error('[Health Check] Backend health check failed:', error);
    
    let message = 'Unknown error';
    if (error instanceof TypeError && error.message.includes('fetch')) {
      message = 'Cannot connect to backend server. Check if server is running and network is accessible.';
    } else if (error instanceof Error) {
      message = error.message;
    }
    
    return {
      success: false,
      message,
      url: healthUrl
    };
  }
};
