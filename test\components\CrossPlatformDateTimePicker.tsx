import React from 'react';
import { Platform, View, StyleSheet } from 'react-native';

// Types for cross-platform compatibility
export interface DateTimePickerEvent {
  type: 'set' | 'dismissed';
  nativeEvent?: {
    timestamp?: number;
  };
}

export interface CrossPlatformDateTimePickerProps {
  value: Date;
  mode: 'date' | 'time' | 'datetime';
  display?: 'default' | 'spinner' | 'compact';
  onChange: (event: DateTimePickerEvent, selectedDate?: Date) => void;
  minimumDate?: Date;
  maximumDate?: Date;
  style?: any;
}

// Web implementation using HTML5 input
const WebDateTimePicker: React.FC<CrossPlatformDateTimePickerProps> = ({
  value,
  mode,
  onChange,
  minimumDate,
  maximumDate,
  style,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = new Date(event.target.value);
    if (!isNaN(newDate.getTime())) {
      onChange({ type: 'set' }, newDate);
    }
  };

  const formatDateForInput = (date: Date, inputMode: string) => {
    if (inputMode === 'date') {
      return date.toISOString().split('T')[0];
    } else if (inputMode === 'time') {
      return date.toTimeString().split(' ')[0].substring(0, 5);
    } else if (inputMode === 'datetime-local') {
      const offset = date.getTimezoneOffset();
      const localDate = new Date(date.getTime() - (offset * 60 * 1000));
      return localDate.toISOString().split('.')[0];
    }
    return '';
  };

  const getInputType = () => {
    switch (mode) {
      case 'date':
        return 'date';
      case 'time':
        return 'time';
      case 'datetime':
        return 'datetime-local';
      default:
        return 'date';
    }
  };

  const inputType = getInputType();
  const formattedValue = formatDateForInput(value, inputType);

  return (
    <View style={[styles.webContainer, style]}>
      <input
        type={inputType}
        value={formattedValue}
        onChange={handleChange}
        min={minimumDate ? formatDateForInput(minimumDate, inputType) : undefined}
        max={maximumDate ? formatDateForInput(maximumDate, inputType) : undefined}
        style={styles.webInput}
      />
    </View>
  );
};

// Native implementation using @react-native-community/datetimepicker
let NativeDateTimePicker: React.FC<CrossPlatformDateTimePickerProps> | null = null;

if (Platform.OS !== 'web') {
  try {
    const DateTimePicker = require('@react-native-community/datetimepicker').default;
    
    NativeDateTimePicker = ({ onChange, ...props }) => {
      const handleNativeChange = (event: any, selectedDate?: Date) => {
        // Convert native event to our cross-platform format
        const crossPlatformEvent: DateTimePickerEvent = {
          type: event.type === 'set' ? 'set' : 'dismissed',
          nativeEvent: event.nativeEvent,
        };
        onChange(crossPlatformEvent, selectedDate);
      };

      return <DateTimePicker {...props} onChange={handleNativeChange} />;
    };
  } catch (error) {
    console.warn('DateTimePicker not available:', error);
  }
}

// Main cross-platform component
export const CrossPlatformDateTimePicker: React.FC<CrossPlatformDateTimePickerProps> = (props) => {
  if (Platform.OS === 'web') {
    return <WebDateTimePicker {...props} />;
  } else if (NativeDateTimePicker) {
    return <NativeDateTimePicker {...props} />;
  } else {
    // Fallback for when native picker is not available
    return <WebDateTimePicker {...props} />;
  }
};

const styles = StyleSheet.create({
  webContainer: {
    padding: 8,
  },
  webInput: Platform.select({
    web: {
      padding: 12,
      fontSize: 16,
      borderRadius: 8,
      border: '1px solid #ccc',
      backgroundColor: '#fff',
      color: '#000',
      width: '100%',
      boxSizing: 'border-box',
    },
    default: {},
  }) as any, // Type assertion for web-specific styles
});

export default CrossPlatformDateTimePicker;
