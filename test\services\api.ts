// API Service for BAAM Backend Integration
import { Goal, NewTaskForm, Task, User } from '../types';
import { fetchWithAuth } from './auth';
import { auth } from './firebase';
import { API_BASE_URL } from '../config/api';

// Helper to get the current user's ID
const getCurrentUserId = () => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('No authenticated user found.');
  }
  return user.uid;
};

// Generic API request function using fetchWithAuth
async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  const response = await fetchWithAuth(url, options);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }

  // For DELETE requests, the body might be empty
  if (options.method === 'DELETE' || response.status === 204) {
    return {} as T;
  }

  return response.json();
}

// Task API functions
export const taskAPI = {
  // Get all tasks for the current user
  async getTasks(): Promise<Task[]> {
    const uid = getCurrentUserId();
    return apiRequest<Task[]>(`/api/users/${uid}/tasks`);
  },

  // Get a specific task
  async getTask(taskId: string): Promise<Task> {
    return apiRequest<Task>(`/api/tasks/${taskId}`);
  },

  // Create a new task
  async createTask(taskData: NewTaskForm): Promise<{ task_id: string; message: string }> {
    const uid = getCurrentUserId();
    const backendTask = {
      uid,
      title: taskData.title,
      description: taskData.notes || '',
      status: 'planned',
      estimated_duration: taskData.startTime && taskData.endTime 
        ? calculateDuration(taskData.startTime, taskData.endTime) 
        : 60, // Default 1 hour
      flexibility: taskData.isFlexible ? 8 : 2,
      categories: ['user-created'],
      scheduled_time: taskData.startTime ? formatScheduledTime(taskData.startTime) : undefined,
      is_recurring: taskData.isRecurring,
    };

    return apiRequest<{ task_id: string; message: string }>('/api/tasks', {
      method: 'POST',
      body: JSON.stringify(backendTask),
    });
  },

  // Update a task
  async updateTask(taskId: string, updates: Partial<Task>): Promise<{ message: string }> {
    const backendUpdates: Record<string, any> = {};
    if (updates.title !== undefined) backendUpdates.title = updates.title;
    // Prefer description field; some callers might still use `notes`
    if ((updates as any).notes !== undefined) backendUpdates.description = (updates as any).notes;
    if (updates.description !== undefined) backendUpdates.description = updates.description;
    // Pass through status if provided; fallback to completed boolean for backward compatibility
    if (updates.status !== undefined) backendUpdates.status = updates.status;
    else if ((updates as any).completed !== undefined) backendUpdates.status = (updates as any).completed ? 'completed' : 'planned';

    return apiRequest<{ message: string }>(`/api/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify(backendUpdates),
    });
  },

  // Delete a task
  async deleteTask(taskId: string): Promise<{ message: string }> {
    return apiRequest<{ message: string }>(`/api/tasks/${taskId}`, {
      method: 'DELETE',
    });
  },

  // Schedule a task using AI
  async scheduleTask(taskId: string, preferredTime?: string): Promise<any> {
    const scheduleData = preferredTime ? { preferred_time: preferredTime } : {};
    
    return apiRequest(`/api/tasks/${taskId}/schedule`, {
      method: 'POST',
      body: JSON.stringify(scheduleData),
    });
  },
};

// Goal API functions
export const goalAPI = {
  // Get all goals for the current user
  async getGoals(): Promise<Goal[]> {
    const uid = getCurrentUserId();
    return apiRequest<Goal[]>(`/api/users/${uid}/goals`);
  },

  // Create a new goal
  async createGoal(goalData: Partial<Goal>): Promise<{ goal_id: string; message: string }> {
    const uid = getCurrentUserId();
    const backendGoal = {
      uid,
      title: goalData.title,
      description: goalData.description || '',
      status: 'active',
      target_date: goalData.target_date,
      categories: goalData.categories || ['user-created'],
    };

    return apiRequest<{ goal_id: string; message: string }>('/api/goals', {
      method: 'POST',
      body: JSON.stringify(backendGoal),
    });
  },
};

// User API functions
export const userAPI = {
  // Get current user
  async getUser(): Promise<User> {
    const uid = getCurrentUserId();
    return apiRequest<User>(`/api/users/${uid}`);
  },

  // Get user's daily schedule
  async getDailySchedule(date?: string): Promise<any> {
    const uid = getCurrentUserId();
    const dateParam = date || new Date().toISOString().split('T')[0];
    return apiRequest(`/api/users/${uid}/schedule/daily?date=${dateParam}`);
  },

  // Get productivity insights
  async getInsights(): Promise<any> {
    const uid = getCurrentUserId();
    return apiRequest(`/api/users/${uid}/insights`);
  },
};

// Handle Availability API
export const handleAPI = {
  async checkAvailability(handle: string): Promise<{ is_taken: boolean }> {
    if (!handle) {
      return { is_taken: true }; // Or some other default to prevent empty handles
    }

    const url = `${API_BASE_URL}/api/handles/${handle}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add timeout for mobile networks
        signal: AbortSignal.timeout(10000), // 10 second timeout
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Handle checking service not available.');
        } else if (response.status >= 500) {
          throw new Error('Server error while checking handle.');
        } else {
          throw new Error(`Failed to check handle availability (${response.status}).`);
        }
      }

      return response.json();
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        // Network error - likely can't reach the server
        throw new Error('Could not connect to server. Please check your network connection.');
      }
      throw error;
    }
  },
};

// AI Chat API
export const chatAPI = {
  async sendMessage(message: string, sessionId?: string): Promise<any> {
    return apiRequest('/api/chat', {
      method: 'POST',
      body: JSON.stringify({
        message,
        session_id: sessionId || 'default',
      }),
    });
  },
};

// Utility functions
function calculateDuration(startTime: string, endTime: string): number {
  const [startHour, startMin] = startTime.split(':').map(Number);
  const [endHour, endMin] = endTime.split(':').map(Number);
  
  const startMinutes = startHour * 60 + startMin;
  const endMinutes = endHour * 60 + endMin;
  
  return Math.max(endMinutes - startMinutes, 15); // Minimum 15 minutes
}

function formatScheduledTime(time: string): string {
  const today = new Date().toISOString().split('T')[0];
  return `${today}T${time}:00`;
}

// Export all APIs
export const api = {
  tasks: taskAPI,
  goals: goalAPI,
  user: userAPI,
  chat: chatAPI,
  handle: handleAPI,
};
